import mapboxgl from "mapbox-gl";
import { CALIFORNIA_CENTER, CALIFORNIA_BOUNDS } from "@/types/daycare";

/**
 * 地图核心管理器
 * 负责地图实例的初始化、配置和生命周期管理
 */
export class MapManager {
  private map: mapboxgl.Map | null = null;
  private container: HTMLDivElement | null = null;

  constructor() {
    this.initializeMapbox();
  }

  /**
   * 初始化 Mapbox 配置
   */
  private initializeMapbox(): void {
    if (!process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN) {
      throw new Error("Mapbox access token is required");
    }
    mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN;
  }

  /**
   * 创建地图实例
   */
  createMap(container: HTMLDivElement): mapboxgl.Map {
    if (this.map) {
      this.destroyMap();
    }

    this.container = container;
    this.map = new mapboxgl.Map({
      container,
      style: "mapbox://styles/mapbox/light-v11",
      center: CALIFORNIA_CENTER,
      zoom: 7,
      maxBounds: CALIFORNIA_BOUNDS,
      attributionControl: false,
    });

    this.addControls();
    return this.map;
  }

  /**
   * 添加地图控件
   */
  private addControls(): void {
    if (!this.map) return;

    // 添加导航控件
    this.map.addControl(new mapboxgl.NavigationControl(), "top-right");

    // 添加比例尺
    this.map.addControl(
      new mapboxgl.ScaleControl({
        maxWidth: 100,
        unit: "metric",
      }),
      "bottom-right"
    );
  }

  /**
   * 获取地图实例
   */
  getMap(): mapboxgl.Map | null {
    return this.map;
  }

  /**
   * 检查地图是否已初始化
   */
  isMapReady(): boolean {
    return this.map !== null && this.map.isStyleLoaded();
  }

  /**
   * 飞行到指定位置
   */
  flyTo(coordinates: [number, number], zoom: number = 12): void {
    if (!this.map) return;

    this.map.flyTo({
      center: coordinates,
      zoom,
      duration: 2000,
    });
  }

  /**
   * 设置地图事件监听器
   */
  onLoad(callback: () => void): void {
    if (!this.map) return;
    this.map.on("load", callback);
  }

  /**
   * 设置地图错误监听器
   */
  onError(callback: (error: any) => void): void {
    if (!this.map) return;
    this.map.on("error", callback);
  }

  /**
   * 销毁地图实例
   */
  destroyMap(): void {
    if (this.map) {
      this.map.remove();
      this.map = null;
    }
    this.container = null;
  }

  /**
   * 获取地图容器
   */
  getContainer(): HTMLDivElement | null {
    return this.container;
  }

  /**
   * 检查图层是否存在
   */
  hasLayer(layerId: string): boolean {
    return this.map ? !!this.map.getLayer(layerId) : false;
  }

  /**
   * 检查数据源是否存在
   */
  hasSource(sourceId: string): boolean {
    return this.map ? !!this.map.getSource(sourceId) : false;
  }

  /**
   * 移除图层
   */
  removeLayer(layerId: string): void {
    if (this.map && this.hasLayer(layerId)) {
      this.map.removeLayer(layerId);
    }
  }

  /**
   * 移除数据源
   */
  removeSource(sourceId: string): void {
    if (this.map && this.hasSource(sourceId)) {
      this.map.removeSource(sourceId);
    }
  }

  /**
   * 添加数据源
   */
  addSource(sourceId: string, source: mapboxgl.AnySourceData): void {
    if (!this.map) return;
    this.map.addSource(sourceId, source);
  }

  /**
   * 添加图层
   */
  addLayer(layer: mapboxgl.AnyLayer): void {
    if (!this.map) return;
    this.map.addLayer(layer);
  }

  /**
   * 添加点击事件监听器
   */
  onClick(layerId: string, callback: (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => void): void {
    if (!this.map) return;
    this.map.on("click", layerId, callback);
  }

  /**
   * 添加鼠标进入事件监听器
   */
  onMouseEnter(layerId: string, callback: () => void): void {
    if (!this.map) return;
    this.map.on("mouseenter", layerId, callback);
  }

  /**
   * 添加鼠标离开事件监听器
   */
  onMouseLeave(layerId: string, callback: () => void): void {
    if (!this.map) return;
    this.map.on("mouseleave", layerId, callback);
  }

  /**
   * 设置鼠标样式
   */
  setCursor(cursor: string): void {
    if (!this.map) return;
    this.map.getCanvas().style.cursor = cursor;
  }
}
