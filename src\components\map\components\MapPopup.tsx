"use client";

import React, { useEffect } from "react";
import mapboxgl from "mapbox-gl";
import type { DaycareFeature } from "@/types/daycare";
import { useMapLanguage } from "@/hooks/useMapLanguage";

interface MapPopupProps {
  popup: mapboxgl.Popup;
  feature: DaycareFeature;
}

/**
 * 地图弹窗组件
 * 负责显示托儿所数据的详细信息
 */
export const MapPopup: React.FC<MapPopupProps> = ({ popup, feature }) => {
  const { t } = useMapLanguage();

  useEffect(() => {
    if (popup && feature) {
      const htmlContent = createPopupHTML(feature.properties, t);
      popup.setHTML(htmlContent);
    }
  }, [popup, feature, t]);

  return null; // 这个组件不渲染任何内容，只是管理弹窗的HTML
};

/**
 * 获取饱和度级别的翻译文本
 */
const getSaturationLevelText = (level: string, t: (key: string) => string): string => {
  switch (level) {
    case "low":
      return t("map:legend.lowSaturation");
    case "medium":
      return t("map:legend.mediumSaturation");
    case "high":
      return t("map:legend.highSaturation");
    case "very-high":
      return t("map:legend.veryHighSaturation");
    default:
      return level;
  }
};

/**
 * 创建弹窗HTML内容
 */
const createPopupHTML = (props: DaycareFeature["properties"], t: (key: string) => string): string => {
  const saturationLevel = props.saturation_level.replace("-", "-");
  const saturationLevelText = getSaturationLevelText(props.saturation_level, t);

  return `
    <div class="daycare-popup">
      <div class="popup-header">
        <div class="popup-close" onclick="this.closest('.mapboxgl-popup').remove()"></div>
        <h3>${t("map:popup.title")}</h3>
        <div class="zip-badge">${t("map:popup.zipCode")} ${props.zip_code}</div>
      </div>

      <div class="popup-content">
        <div class="popup-stats">
          <div class="stat-item">
            <div class="stat-value">${props.total_capacity.toLocaleString()}</div>
            <div class="stat-label">${t("map:popup.totalCapacity")}</div>
          </div>

          <div class="stat-item">
            <div class="stat-value">${props.total_births.toLocaleString()}</div>
            <div class="stat-label">${t("map:popup.totalBirths")}</div>
          </div>
        </div>

        <div class="saturation-indicator ${saturationLevel}">
          <div>
            <div class="saturation-text">${saturationLevelText}</div>
            <div style="font-size: 12px; color: #64748b; margin-top: 2px;">${t("map:popup.saturationLevel")}</div>
          </div>
          <div class="saturation-value">${props.saturation.toFixed(2)}</div>
        </div>
      </div>
    </div>
  `;
};

/**
 * 弹窗样式组件
 * 提供全局样式，应该在应用的根级别使用
 */
export const MapPopupStyles: React.FC = () => {
  return (
    <style jsx global>{`
      /* Mapbox弹窗基础样式 */
      .mapboxgl-popup {
        max-width: 320px !important;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif !important;
      }

      .mapboxgl-popup-content {
        padding: 0 !important;
        border-radius: 16px !important;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15),
          0 4px 12px rgba(0, 0, 0, 0.1) !important;
        border: 1px solid rgba(0, 0, 0, 0.05) !important;
        background: linear-gradient(
          135deg,
          #ffffff 0%,
          #f8fafc 100%
        ) !important;
        overflow: hidden !important;
        backdrop-filter: blur(10px) !important;
        transform: none !important;
        animation: none !important;
        transition: none !important;
      }

      .mapboxgl-popup-close-button {
        display: none !important;
      }

      .mapboxgl-popup-tip {
        border-top-color: white !important;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1)) !important;
      }

      /* 弹窗内容样式 */
      .daycare-popup {
        background: transparent;
        border-radius: 16px;
        overflow: hidden;
      }

      .popup-header {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        padding: 16px 20px;
        position: relative;
        overflow: hidden;
      }

      .popup-header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") !important;
        pointer-events: none;
      }

      .popup-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        position: relative;
        z-index: 1;
      }

      .popup-header .zip-badge {
        display: inline-block;
        background: rgba(255, 255, 255, 0.2);
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        margin-top: 4px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .popup-content {
        padding: 20px;
        background: white;
      }

      .popup-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 16px;
      }

      .stat-item {
        text-align: center;
        padding: 12px;
        background: #f8fafc;
        border-radius: 12px;
        border: 1px solid #e2e8f0;
        transition: all 0.2s ease;
      }

      .stat-item:hover {
        background: #f1f5f9;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      }

      .stat-value {
        font-size: 20px;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 12px;
        color: #64748b;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .saturation-indicator {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background: #f8fafc;
        border-radius: 12px;
        border-left: 4px solid;
        margin-top: 12px;
      }

      .saturation-indicator.low {
        border-left-color: #22c55e;
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
      }

      .saturation-indicator.medium {
        border-left-color: #eab308;
        background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
      }

      .saturation-indicator.high {
        border-left-color: #f97316;
        background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
      }

      .saturation-indicator.very-high {
        border-left-color: #ef4444;
        background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
      }

      .saturation-text {
        font-weight: 600;
        font-size: 14px;
      }

      .saturation-value {
        font-size: 18px;
        font-weight: 700;
      }

      .popup-close {
        position: absolute;
        top: 12px;
        right: 12px;
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        backdrop-filter: blur(10px);
        z-index: 2;
      }

      .popup-close:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
      }

      .popup-close::before,
      .popup-close::after {
        content: "";
        position: absolute;
        width: 12px;
        height: 2px;
        background: white;
        border-radius: 1px;
      }

      .popup-close::before {
        transform: rotate(45deg);
      }

      .popup-close::after {
        transform: rotate(-45deg);
      }

      /* 移除动画效果，直接显示 */
      .mapboxgl-popup {
        /* 不使用动画，直接显示 */
      }

      /* 响应式设计 */
      @media (max-width: 480px) {
        .mapboxgl-popup {
          max-width: 280px !important;
        }

        .popup-header {
          padding: 14px 16px;
        }

        .popup-content {
          padding: 16px;
        }

        .popup-stats {
          grid-template-columns: 1fr;
          gap: 12px;
        }
      }
    `}</style>
  );
};
