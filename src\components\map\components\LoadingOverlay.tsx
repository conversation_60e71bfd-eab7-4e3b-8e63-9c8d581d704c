"use client";

import React from "react";
import { useMapLanguage } from "@/hooks/useMapLanguage";

interface LoadingOverlayProps {
  isLoading: boolean;
  loadingStatus: string;
  className?: string;
}

/**
 * 地图加载状态覆盖层组件
 * 显示地图数据加载进度和状态
 */
export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  loadingStatus,
  className = "",
}) => {
  const { t } = useMapLanguage();

  if (!isLoading) {
    return null;
  }

  return (
    <div className={`absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-20 ${className}`}>
      <div className="text-center">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
        <div className="text-gray-700 font-medium">{loadingStatus}</div>
        <div className="text-gray-500 text-sm mt-2">
          {t("map:loading.pleaseWait")}
        </div>
      </div>
    </div>
  );
};
