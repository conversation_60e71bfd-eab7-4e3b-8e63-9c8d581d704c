import type { DaycareGeoJSON } from "@/types/daycare";
import { MapManager } from "./MapManager";

/**
 * 图层管理器
 * 负责管理地图图层的添加、移除和样式配置
 */
export class LayerManager {
  private mapManager: MapManager;

  constructor(mapManager: MapManager) {
    this.mapManager = mapManager;
  }

  /**
   * 将ZIP区域数据添加到地图
   */
  addZipAreasToMap(mergedData: any): void {
    const map = this.mapManager.getMap();
    if (!map) return;

    // 检查并移除已存在的图层和数据源
    this.removeExistingLayers();

    // 添加数据源
    this.mapManager.addSource("daycare-zip-areas", {
      type: "geojson",
      data: mergedData,
    });

    // 添加填充图层 - 根据饱和度显示不同颜色
    this.mapManager.addLayer({
      id: "daycare-zip-fill",
      type: "fill",
      source: "daycare-zip-areas",
      paint: {
        // 根据饱和度等级设置颜色
        "fill-color": [
          "match",
          ["get", "saturation_level"],
          "low",
          "#22c55e", // 绿色 - 低饱和度
          "medium",
          "#eab308", // 黄色 - 中等饱和度
          "high",
          "#f97316", // 橙色 - 高饱和度
          "very-high",
          "#ef4444", // 红色 - 极高饱和度
          "#6b7280", // 默认灰色
        ],
        // 根据饱和度调整透明度
        "fill-opacity": [
          "interpolate",
          ["linear"],
          ["get", "saturation"],
          0,
          0.3, // 最低饱和度 -> 30%透明度
          1,
          0.5, // 低饱和度 -> 50%透明度
          2,
          0.7, // 中等饱和度 -> 70%透明度
          5,
          0.8, // 高饱和度 -> 80%透明度
          50,
          0.9, // 极高饱和度 -> 90%透明度
        ],
      },
    });

    // 添加边界线图层
    this.mapManager.addLayer({
      id: "daycare-zip-stroke",
      type: "line",
      source: "daycare-zip-areas",
      paint: {
        "line-color": "#ffffff",
        "line-width": [
          "interpolate",
          ["linear"],
          ["zoom"],
          6,
          0.5, // 低缩放级别时更细
          10,
          1, // 中等缩放级别
          14,
          1.5, // 高缩放级别时更粗
        ],
        "line-opacity": 0.8,
      },
    });

    // 添加ZIP码文本图层
    this.mapManager.addLayer({
      id: "daycare-zip-labels",
      type: "symbol",
      source: "daycare-zip-areas",
      layout: {
        "text-field": ["get", "zip_code"],
        "text-font": ["Open Sans Semibold", "Arial Unicode MS Bold"],
        "text-size": [
          "interpolate",
          ["linear"],
          ["zoom"],
          8,
          10, // 低缩放级别时较小
          10,
          12, // 中等缩放级别
          12,
          14, // 高缩放级别时较大
          14,
          16,
        ],
        "text-anchor": "center",
        "text-justify": "center",
        "text-allow-overlap": false,
        "text-ignore-placement": false,
        "symbol-placement": "point",
      },
      paint: {
        "text-color": "#ffffff",
        "text-halo-color": "#000000",
        "text-halo-width": 1.5,
        "text-opacity": [
          "interpolate",
          ["linear"],
          ["zoom"],
          8,
          0.6, // 低缩放级别时较透明
          10,
          0.8, // 中等缩放级别
          12,
          1.0, // 高缩放级别时完全不透明
        ],
      },
      // 只在较高缩放级别显示文本，避免过于拥挤
      minzoom: 9,
    });
  }

  /**
   * 将数据添加到地图（圆点模式 - 备用）
   */
  addDataToMap(geoJsonData: DaycareGeoJSON): void {
    const map = this.mapManager.getMap();
    if (!map) return;

    // 检查并移除已存在的图层和数据源
    this.removeExistingLayers();

    // 添加数据源
    this.mapManager.addSource("daycare-data-2023", {
      type: "geojson",
      data: geoJsonData,
    });

    // 添加圆点图层
    this.mapManager.addLayer({
      id: "daycare-circles",
      type: "circle",
      source: "daycare-data-2023",
      paint: {
        // 根据饱和度调整圆点大小
        "circle-radius": [
          "interpolate",
          ["linear"],
          ["get", "saturation"],
          0,
          8, // 最小饱和度 -> 8px
          1,
          10, // 低饱和度 -> 10px
          5,
          14, // 中等饱和度 -> 14px
          10,
          18, // 高饱和度 -> 18px
          20,
          22, // 很高饱和度 -> 22px
          50,
          26, // 极高饱和度 -> 26px
        ],
        // 根据饱和度等级设置颜色
        "circle-color": [
          "match",
          ["get", "saturation_level"],
          "low",
          "#22c55e", // 绿色 - 低饱和度
          "medium",
          "#eab308", // 黄色 - 中等饱和度
          "high",
          "#f97316", // 橙色 - 高饱和度
          "very-high",
          "#ef4444", // 红色 - 极高饱和度
          "#6b7280", // 默认灰色
        ],
        "circle-opacity": 0.8,
        "circle-stroke-width": 2,
        "circle-stroke-color": "#ffffff",
      },
    });
  }

  /**
   * 将ZIP边界添加到地图
   */
  async addZipBoundariesToMap(geoJsonData: any): Promise<void> {
    const map = this.mapManager.getMap();
    if (!map || !geoJsonData) return;

    // 使用 requestAnimationFrame 来避免阻塞UI
    await new Promise((resolve) => requestAnimationFrame(resolve));

    // 检查并移除已存在的ZIP边界图层和数据源
    this.mapManager.removeLayer("zip-boundaries-fill");
    this.mapManager.removeLayer("zip-boundaries-line");
    this.mapManager.removeSource("zip-boundaries");

    // 添加ZIP边界数据源
    this.mapManager.addSource("zip-boundaries", {
      type: "geojson",
      data: geoJsonData,
      // 性能优化选项
      lineMetrics: false,
      generateId: true,
      buffer: 0, // 减少缓冲区
      tolerance: 0.375, // 简化几何形状
    });

    // 使用 requestAnimationFrame 分批添加图层
    await new Promise((resolve) => requestAnimationFrame(resolve));

    // 添加填充图层（半透明）
    this.mapManager.addLayer({
      id: "zip-boundaries-fill",
      type: "fill",
      source: "zip-boundaries",
      paint: {
        "fill-color": "#3b82f6",
        "fill-opacity": [
          "interpolate",
          ["linear"],
          ["zoom"],
          6,
          0.05, // 低缩放级别时更透明
          10,
          0.1, // 中等缩放级别
          14,
          0.15, // 高缩放级别时更明显
        ],
      },
      // 只在特定缩放级别显示以提高性能
      minzoom: 6,
    });

    await new Promise((resolve) => requestAnimationFrame(resolve));

    // 添加边界线图层
    this.mapManager.addLayer({
      id: "zip-boundaries-line",
      type: "line",
      source: "zip-boundaries",
      paint: {
        "line-color": "#3b82f6",
        "line-width": [
          "interpolate",
          ["linear"],
          ["zoom"],
          6,
          0.3, // 低缩放级别时更细
          10,
          0.8, // 中等缩放级别
          14,
          1.5, // 高缩放级别时更粗
        ],
        "line-opacity": [
          "interpolate",
          ["linear"],
          ["zoom"],
          6,
          0.4, // 低缩放级别时更透明
          10,
          0.7, // 中等缩放级别
          14,
          0.9, // 高缩放级别时更明显
        ],
      },
      minzoom: 6,
    });
  }

  /**
   * 隐藏ZIP边界
   */
  hideZipBoundaries(): void {
    this.mapManager.removeLayer("zip-boundaries-fill");
    this.mapManager.removeLayer("zip-boundaries-line");
    this.mapManager.removeSource("zip-boundaries");
  }

  /**
   * 移除已存在的图层和数据源
   */
  removeExistingLayers(): void {
    // 移除ZIP区域图层
    this.mapManager.removeLayer("daycare-zip-labels");
    this.mapManager.removeLayer("daycare-zip-stroke");
    this.mapManager.removeLayer("daycare-zip-fill");
    this.mapManager.removeSource("daycare-zip-areas");

    // 移除圆点图层
    this.mapManager.removeLayer("daycare-circles");
    this.mapManager.removeSource("daycare-data-2023");
  }
}
