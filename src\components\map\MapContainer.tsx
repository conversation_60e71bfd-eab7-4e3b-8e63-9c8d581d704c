"use client";

import React, { useRef, useEffect, useState } from "react";
import { useMapLanguage } from "@/hooks/useMapLanguage";
import type { MapContainerProps, DaycareFeature } from "@/types/daycare";
import { LoadingOverlay } from "./components/LoadingOverlay";
import { MapControls } from "./components/MapControls";
import { MapPopup, MapPopupStyles } from "./components/MapPopup";
import { EventHandler } from "./core/EventHandler";
import { LayerManager } from "./core/LayerManager";
import { MapManager } from "./core/MapManager";
import { useMapData } from "./hooks/useMapData";
import { useMapState } from "./hooks/useMapState";

import "mapbox-gl/dist/mapbox-gl.css";

/**
 * 地图容器组件 - 负责Mapbox地图的初始化和数据展示
 */
const MapContainer: React.FC<MapContainerProps> = ({
  className = "",
  height = "100%",
  onMapLoad,
  onError,
}) => {
  const { t } = useMapLanguage();
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapManagerRef = useRef<MapManager | null>(null);
  const layerManagerRef = useRef<LayerManager | null>(null);
  const eventHandlerRef = useRef<EventHandler | null>(null);

  // 使用自定义hooks管理状态和数据
  const mapState = useMapState();
  const mapData = useMapData();
  const [currentPopup, setCurrentPopup] = useState<{
    popup: any;
    feature: DaycareFeature;
  } | null>(null);

  // 初始化地图
  useEffect(() => {
    if (!mapContainerRef.current) {
      return;
    }

    // 设置初始加载状态
    mapState.updateLoadingStatus(t("map:loading.initializing"));

    try {
      // 创建地图管理器
      const mapManager = new MapManager();
      mapManagerRef.current = mapManager;

      // 创建地图实例
      const map = mapManager.createMap(mapContainerRef.current);

      // 创建图层管理器
      const layerManager = new LayerManager(mapManager);
      layerManagerRef.current = layerManager;

      // 创建事件处理器
      const eventHandler = new EventHandler(mapManager);
      eventHandlerRef.current = eventHandler;

      // 设置弹窗创建回调
      eventHandler.setPopupCreateCallback(
        (popup: any, feature: DaycareFeature) => {
          setCurrentPopup({ popup, feature });
        }
      );

      // 地图加载完成事件
      mapManager.onLoad(async () => {
        mapState.setMapLoaded(true);
        mapState.updateLoadingStatus(t("map:loading.loadingBoundaries"));

        // 先预加载ZIP边界数据，然后加载托儿所数据
        const zipData = await mapData.preloadZipBoundaries();

        mapState.updateLoadingStatus(t("map:loading.loadingData"));
        await loadDaycareDataAndAddToMap(zipData, layerManager, eventHandler);

        mapState.updateLoadingStatus(t("map:loading.complete"));
        mapState.setLoadingState(false);
        onMapLoad?.();
      });

      // 错误处理
      mapManager.onError((e) => {
        console.error("Map load error:", e);
        mapState.setErrorState(new Error("地图加载失败"));
        onError?.(new Error("地图加载失败"));
      });

      // 初始化飞行到位置事件监听
      const cleanupFlyTo = eventHandler.initializeFlyToEvents();

      // 清理函数
      return () => {
        cleanupFlyTo?.();
        eventHandler.cleanup();
        mapManager.destroyMap();
        mapManagerRef.current = null;
        layerManagerRef.current = null;
        eventHandlerRef.current = null;
      };
    } catch (error) {
      mapState.updateLoadingStatus("地图加载失败");
      mapState.setLoadingState(false);
      mapState.setErrorState(error as Error);
      onError?.(error as Error);
    }
  }, [onMapLoad, onError, t, mapState, mapData]);

  // 加载托儿所数据并添加到地图
  const loadDaycareDataAndAddToMap = async (
    zipData: any,
    layerManager: LayerManager,
    eventHandler: EventHandler
  ): Promise<void> => {
    try {
      const result = await mapData.loadDaycareData(zipData);

      if (result.type === "zip-areas") {
        // 添加ZIP区域图层
        layerManager.addZipAreasToMap(result.data);
        eventHandler.initializeZipAreaEvents();
      } else if (result.type === "points") {
        // 添加点图层
        layerManager.addDataToMap(result.data);
        eventHandler.initializeCircleEvents();
      }
    } catch (error) {
      console.error("Error loading and adding daycare data:", error);
      mapState.setErrorState(error as Error);
      onError?.(error as Error);
    }
  };

  // ZIP边界控制逻辑
  const handleToggleZipBoundaries = async () => {
    const layerManager = layerManagerRef.current;
    if (!layerManager) {
      return;
    }

    mapState.toggleZipBoundaries();

    if (!mapState.showZipBoundaries) {
      // 显示ZIP边界
      mapState.setBoundariesRendering(true);

      let zipData = mapData.zipBoundariesData;
      if (!zipData) {
        zipData = await mapData.preloadZipBoundaries();
      }

      if (zipData) {
        await layerManager.addZipBoundariesToMap(zipData);
      }

      mapState.setBoundariesRendering(false);
    } else {
      // 隐藏ZIP边界
      layerManager.hideZipBoundaries();
    }
  };

  return (
    <>
      <div className="relative" style={{ height }}>
        <div
          ref={mapContainerRef}
          className={`map-container ${className}`}
          style={{ height: "100%", width: "100%" }}
        />

        {/* 加载状态覆盖层 */}
        <LoadingOverlay
          isLoading={mapState.isLoading}
          loadingStatus={mapState.loadingStatus}
        />

        {/* 地图控制按钮 */}
        <MapControls
          showZipBoundaries={mapState.showZipBoundaries}
          isZipDataLoaded={mapData.isZipDataLoaded}
          isShowingBoundaries={mapState.isShowingBoundaries}
          onToggleZipBoundaries={handleToggleZipBoundaries}
        />

        {/* 弹窗组件 */}
        {currentPopup && (
          <MapPopup popup={currentPopup.popup} feature={currentPopup.feature} />
        )}
      </div>

      {/* 全局弹窗样式 */}
      <MapPopupStyles />
    </>
  );
};

export default MapContainer;
