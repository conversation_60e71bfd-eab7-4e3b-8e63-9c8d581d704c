import type { DaycareGeoJSON } from "@/types/daycare";
import {
  extractZipCoordinates,
  mergeZipBoundariesWithPropertiesData,
} from "@/utils/zipCodeCoordinates";

/**
 * 数据加载管理器
 * 负责处理托儿所数据和ZIP边界数据的加载逻辑
 */
export class DataLoader {
  private zipBoundariesData: any = null;
  private zipCoordinates: Map<string, [number, number]> = new Map();
  private isZipDataLoaded: boolean = false;

  /**
   * 预加载ZIP边界数据
   */
  async preloadZipBoundaries(): Promise<any> {
    if (this.isZipDataLoaded) {
      return this.zipBoundariesData;
    }

    try {
      // 创建一个带超时的fetch请求
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        controller.abort();
      }, 10000); // 10秒超时

      const response = await fetch("/api/zip-boundaries", {
        signal: controller.signal,
      });
      clearTimeout(timeoutId);

      const result = await response.json();

      if (!result.success) {
        return null;
      }

      this.zipBoundariesData = result.data;

      // 提取ZIP坐标映射
      const coordinates = extractZipCoordinates(result.data);
      this.zipCoordinates = coordinates;

      this.isZipDataLoaded = true;
      return result.data;
    } catch (error) {
      if (error instanceof Error && error.name === "AbortError") {
        console.warn("ZIP数据加载超时");
      } else {
        console.warn("ZIP数据加载失败", error);
      }
      return null;
    }
  }

  /**
   * 加载托儿所数据并与ZIP边界合并
   */
  async loadDaycareData(zipData?: any): Promise<any> {
    try {
      // 使用传入的ZIP数据或状态中的ZIP数据
      const availableZipData = zipData || this.zipBoundariesData;

      // 如果ZIP边界数据已加载，使用属性数据创建ZIP区域显示
      if (availableZipData) {
        const propertiesResponse = await fetch("/api/daycare-properties-2023");
        const propertiesResult = await propertiesResponse.json();

        if (!propertiesResult.success) {
          throw new Error(propertiesResult.error || "属性数据加载失败");
        }

        const mergedData = mergeZipBoundariesWithPropertiesData(
          availableZipData,
          propertiesResult.data
        );

        if (mergedData) {
          return { type: "zip-areas", data: mergedData };
        } else {
          return await this.loadPointData();
        }
      } else {
        return await this.loadPointData();
      }
    } catch (error) {
      console.error("Error loading daycare data:", error);
      return await this.loadPointData();
    }
  }

  /**
   * 加载点数据（回退方案）
   */
  async loadPointData(): Promise<{ type: string; data: DaycareGeoJSON }> {
    const response = await fetch("/api/daycare-data-2023?format=geojson");
    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || "点数据加载失败");
    }

    const daycareGeoJsonData: DaycareGeoJSON = result.data;
    return { type: "points", data: daycareGeoJsonData };
  }

  /**
   * 获取ZIP边界数据
   */
  getZipBoundariesData(): any {
    return this.zipBoundariesData;
  }

  /**
   * 获取ZIP坐标映射
   */
  getZipCoordinates(): Map<string, [number, number]> {
    return this.zipCoordinates;
  }

  /**
   * 检查ZIP数据是否已加载
   */
  isZipDataReady(): boolean {
    return this.isZipDataLoaded;
  }

  /**
   * 重置数据状态
   */
  reset(): void {
    this.zipBoundariesData = null;
    this.zipCoordinates = new Map();
    this.isZipDataLoaded = false;
  }

  /**
   * 设置ZIP边界数据
   */
  setZipBoundariesData(data: any): void {
    this.zipBoundariesData = data;
    if (data) {
      this.zipCoordinates = extractZipCoordinates(data);
      this.isZipDataLoaded = true;
    }
  }
}
