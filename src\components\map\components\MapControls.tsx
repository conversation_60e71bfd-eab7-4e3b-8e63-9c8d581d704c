"use client";

import React from "react";
import { useMapLanguage } from "@/hooks/useMapLanguage";

interface MapControlsProps {
  showZipBoundaries: boolean;
  isZipDataLoaded: boolean;
  isShowingBoundaries: boolean;
  onToggleZipBoundaries: () => void;
  className?: string;
}

/**
 * 地图控制按钮组件
 * 提供ZIP边界显示/隐藏等控制功能
 */
export const MapControls: React.FC<MapControlsProps> = ({
  showZipBoundaries,
  isZipDataLoaded,
  isShowingBoundaries,
  onToggleZipBoundaries,
  className = "",
}) => {
  const { t } = useMapLanguage();

  const getButtonText = (): string => {
    if (!isZipDataLoaded && !showZipBoundaries) {
      return t("map:common.loading");
    }
    if (isShowingBoundaries) {
      return t("map:loading.rendering");
    }
    return showZipBoundaries
      ? t("map:boundaries.hide")
      : t("map:boundaries.show");
  };

  const getButtonTitle = (): string => {
    return showZipBoundaries
      ? t("map:boundaries.hide")
      : t("map:boundaries.show");
  };

  const isDisabled = (): boolean => {
    return (!isZipDataLoaded && !showZipBoundaries) || isShowingBoundaries;
  };

  return (
    <div className={`absolute top-0 right-12 z-10 ${className}`}>
      <button
        onClick={onToggleZipBoundaries}
        className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
          showZipBoundaries
            ? "bg-blue-600 text-white hover:bg-blue-700"
            : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-300"
        }`}
        title={getButtonTitle()}
        disabled={isDisabled()}
      >
        {getButtonText()}
      </button>
    </div>
  );
};
